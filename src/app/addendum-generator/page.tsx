'use client';

import React, { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';

// Types for addendum generation
interface UploadedSOW {
  id: string;
  name: string;
  markdown: string;
  placeholders: string[];
}

interface UploadedAddendumTemplate {
  id: string;
  name: string;
  markdown: string;
  placeholders: string[];
}

interface AddendumAnalysis {
  sowContext: Array<{
    field: string;
    value: string;
    category: string;
    confidence: number;
  }>;
  addendumRequiredFields: Array<{
    field: string;
    label: string;
    type: string;
    category: string;
    required: boolean;
    canPreFill: boolean;
    description?: string;
  }>;
  addendumOptionalFields: Array<{
    field: string;
    label: string;
    type: string;
    category: string;
    required: boolean;
    canPreFill: boolean;
  }>;
  preFillableFields: Array<{
    addendumField: string;
    sowField: string;
    value: string;
    confidence: number;
  }>;
  crossReferences: Array<{
    sowReference: string;
    addendumField: string;
    relationship: string;
  }>;
  addendumType: string;
  complexity: string;
  recommendations: string[];
  missingInformation: Array<{
    field: string;
    reason: string;
    priority: string;
  }>;
}

const AddendumGeneratorPage = () => {
  const [step, setStep] = useState<'upload' | 'analysis' | 'form' | 'processing' | 'preview'>('upload');
  const [sowTemplate, setSOWTemplate] = useState<UploadedSOW | null>(null);
  const [addendumTemplate, setAddendumTemplate] = useState<UploadedAddendumTemplate | null>(null);
  const [addendumAnalysis, setAddendumAnalysis] = useState<AddendumAnalysis | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dragActive, setDragActive] = useState<'sow' | 'addendum' | null>(null);
  const [formData, setFormData] = useState<Record<string, string>>({});
  const [processedMarkdown, setProcessedMarkdown] = useState<string>('');
  const [userPrompt, setUserPrompt] = useState<string>('');
  const [showSOWSelector, setShowSOWSelector] = useState(false);
  const [availableSOWs, setAvailableSOWs] = useState<any[]>([]);
  const [saveAsTemplate, setSaveAsTemplate] = useState(false);
  const [templateName, setTemplateName] = useState('');
  const [uploadSuccess, setUploadSuccess] = useState<{sow: boolean, addendum: boolean}>({sow: false, addendum: false});
  const [uploadedFiles, setUploadedFiles] = useState<{sow: string | null, addendum: string | null}>({sow: null, addendum: null});

  const router = useRouter();

  // Simple notification function
  const showNotification = (message: string, type: 'success' | 'error' = 'success') => {
    // For now, use console.log - can be enhanced later with proper notifications
    console.log(`${type.toUpperCase()}: ${message}`);
  };

  // Fetch available SOWs from dashboard
  const fetchAvailableSOWs = async () => {
    try {
      const response = await fetch('/api/sow', {
        headers: {
          'x-user-id': 'default-user' // This would come from auth context in real app
        }
      });

      if (response.ok) {
        const sows = await response.json();
        setAvailableSOWs(sows);
      }
    } catch (error) {
      console.error('Error fetching SOWs:', error);
    }
  };

  // Handle SOW selection from dashboard
  const handleSOWSelection = async (sow: any) => {
    try {
      // Convert the SOW to the format expected by the addendum generator
      const sowTemplate: UploadedSOW = {
        id: sow.id,
        name: sow.name || `SOW-${sow.id}`,
        markdown: sow.content || sow.markdown || '',
        placeholders: []
      };

      setSOWTemplate(sowTemplate);
      setUploadedFiles(prev => ({ ...prev, sow: sowTemplate.name }));
      setUploadSuccess(prev => ({ ...prev, sow: true }));
      setShowSOWSelector(false);

      // Store the SOW template for later processing
      (window as any).tempSOWTemplate = sowTemplate;

      const addendumFile = (window as any).tempAddendumFile;
      if (addendumFile) {
        // Both SOW and addendum are ready, process them
        handleSOWTemplateAndAddendumUpload(sowTemplate, addendumFile);
        (window as any).tempSOWTemplate = null;
        (window as any).tempAddendumFile = null;
      } else {
        showNotification('SOW selected successfully. Please upload addendum template.', 'success');
      }
    } catch (error) {
      setError('Failed to load selected SOW');
    }
  };

  // Handle SOW template from dashboard + addendum file upload
  const handleSOWTemplateAndAddendumUpload = async (sowTemplate: UploadedSOW, addendumFile: File) => {
    if (!addendumFile.name.toLowerCase().endsWith('.docx')) {
      setError('Addendum file must be DOCX format.');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      console.log('🚀 ADDENDUM UPLOAD: Starting SOW template + addendum file processing - v2');

      const formData = new FormData();
      formData.append('addendumFile', addendumFile);
      formData.append('sowTemplate', JSON.stringify(sowTemplate));

      const response = await fetch('/api/addendum/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }

      const result = await response.json();
      console.log('✅ ADDENDUM UPLOAD: Files processed successfully', result);

      // Set the templates
      setAddendumTemplate({
        id: result.addendumTemplate.id,
        name: result.addendumTemplate.name,
        markdown: result.addendumTemplate.markdown,
        placeholders: result.addendumTemplate.placeholders
      });

      // Check if Gemini analysis was included in the response
      if (result.analysis) {
        console.log('✅ ADDENDUM UPLOAD: Using Gemini analysis from upload');
        setAddendumAnalysis({
          sowTemplateId: result.sowTemplate.id,
          addendumTemplateId: result.addendumTemplate.id,
          analysis: result.analysis
        });
        setStep('form');
        showNotification('Files processed and analyzed successfully!', 'success');
      } else {
        console.log('🔍 ADDENDUM UPLOAD: Running separate Gemini analysis...');
        // Fallback to separate analysis if not included in upload
        const analysisResponse = await fetch('/api/gemini/analyze-addendum', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            sowTemplateId: result.sowTemplate.id,
            addendumTemplateId: result.addendumTemplate.id,
            sowMarkdown: result.sowTemplate.markdown,
            addendumMarkdown: result.addendumTemplate.markdown
          }),
        });

        if (analysisResponse.ok) {
          const analysisResult = await analysisResponse.json();
          setAddendumAnalysis(analysisResult);
          setStep('form');
          showNotification('Files processed and analyzed successfully!', 'success');
        } else {
          setStep('analysis');
          showNotification('Files processed successfully!', 'success');
        }
      }

    } catch (err) {
      console.error('❌ ADDENDUM UPLOAD: Error processing files:', err);
      setError(err instanceof Error ? err.message : 'Failed to process files');
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle dual file upload
  const handleDualFileUpload = async (sowFile: File, addendumFile: File) => {
    if (!sowFile.name.toLowerCase().endsWith('.docx') || !addendumFile.name.toLowerCase().endsWith('.docx')) {
      setError('Both files must be DOCX format.');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      console.log('📤 ADDENDUM UPLOAD: Starting dual file upload...');

      const formData = new FormData();
      formData.append('sowFile', sowFile);
      formData.append('addendumFile', addendumFile);
      formData.append('saveAsTemplate', 'false');

      const response = await fetch('/api/addendum/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }

      const result = await response.json();

      setSOWTemplate({
        id: result.sowTemplate.id,
        name: result.sowTemplate.name,
        markdown: result.sowTemplate.markdown,
        placeholders: result.sowTemplate.placeholders
      });

      setAddendumTemplate({
        id: result.addendumTemplate.id,
        name: result.addendumTemplate.name,
        markdown: result.addendumTemplate.markdown,
        placeholders: result.addendumTemplate.placeholders
      });

      console.log('✅ ADDENDUM UPLOAD: Dual files uploaded successfully');

      // Check if Gemini analysis was included in the upload response
      if (result.analysis) {
        console.log('✅ ADDENDUM UPLOAD: Using Gemini analysis from upload');
        setAddendumAnalysis({
          sowTemplateId: result.sowTemplate.id,
          addendumTemplateId: result.addendumTemplate.id,
          analysis: result.analysis
        });
        setStep('form');
        showNotification('Files processed and analyzed successfully!', 'success');
      } else {
        console.log('🔍 ADDENDUM UPLOAD: Running separate Gemini analysis...');
        // Fallback to separate analysis if not included in upload
        await analyzeDocuments(result.sowTemplate, result.addendumTemplate);
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Upload failed');
    } finally {
      setIsProcessing(false);
    }
  };

  // Analyze both documents with Gemini
  const analyzeDocuments = async (sow: any, addendum: any) => {
    try {
      console.log('🔍 ADDENDUM ANALYSIS: Starting dual-document analysis...');

      const analysisResponse = await fetch('/api/gemini/analyze-addendum', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sowTemplateId: sow.id,
          addendumTemplateId: addendum.id,
          sowMarkdown: sow.markdown,
          addendumMarkdown: addendum.markdown
        }),
      });

      if (!analysisResponse.ok) {
        const errorData = await analysisResponse.json();
        throw new Error(errorData.error || 'Analysis failed');
      }

      const analysisResult = await analysisResponse.json();
      setAddendumAnalysis(analysisResult.analysis);

      console.log('✅ ADDENDUM ANALYSIS: Analysis complete');
      setStep('analysis');

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Analysis failed');
    }
  };

  // Handle drag and drop for SOW file
  const handleSOWDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(null);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const sowFile = e.dataTransfer.files[0];
      (window as any).tempSOWFile = sowFile;
      setUploadedFiles(prev => ({ ...prev, sow: sowFile.name }));
      setUploadSuccess(prev => ({ ...prev, sow: true }));

      const addendumFile = (window as any).tempAddendumFile;
      if (addendumFile) {
        // Both files are ready, process them
        handleDualFileUpload(sowFile, addendumFile);
        (window as any).tempSOWFile = null;
        (window as any).tempAddendumFile = null;
      } else {
        showNotification('SOW file ready. Please upload addendum template.', 'success');
      }
    }
  }, [handleDualFileUpload, showNotification]);

  // Handle drag and drop for Addendum file
  const handleAddendumDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(null);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const addendumFile = e.dataTransfer.files[0];
      (window as any).tempAddendumFile = addendumFile;
      setUploadedFiles(prev => ({ ...prev, addendum: addendumFile.name }));
      setUploadSuccess(prev => ({ ...prev, addendum: true }));

      const sowFile = (window as any).tempSOWFile;
      const sowTemplate = (window as any).tempSOWTemplate;

      if (sowFile) {
        // Both files are ready, process them
        handleDualFileUpload(sowFile, addendumFile);
        (window as any).tempSOWFile = null;
        (window as any).tempAddendumFile = null;
      } else if (sowTemplate) {
        // SOW template from dashboard + addendum file
        handleSOWTemplateAndAddendumUpload(sowTemplate, addendumFile);
        (window as any).tempSOWTemplate = null;
        (window as any).tempAddendumFile = null;
      } else {
        showNotification('Addendum template ready. Please upload SOW file or select from dashboard.', 'success');
      }
    }
  }, [handleDualFileUpload, handleSOWTemplateAndAddendumUpload, showNotification]);

  // Handle file input changes
  const handleSOWFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const sowFile = e.target.files[0];
      (window as any).tempSOWFile = sowFile;
      setUploadedFiles(prev => ({ ...prev, sow: sowFile.name }));
      setUploadSuccess(prev => ({ ...prev, sow: true }));

      const addendumFile = (window as any).tempAddendumFile;
      if (addendumFile) {
        // Both files are ready, process them
        handleDualFileUpload(sowFile, addendumFile);
        (window as any).tempSOWFile = null;
        (window as any).tempAddendumFile = null;
      } else {
        showNotification('SOW file ready. Please upload addendum template.', 'success');
      }
    }
  };

  const handleAddendumFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const addendumFile = e.target.files[0];
      (window as any).tempAddendumFile = addendumFile;
      setUploadedFiles(prev => ({ ...prev, addendum: addendumFile.name }));
      setUploadSuccess(prev => ({ ...prev, addendum: true }));

      const sowFile = (window as any).tempSOWFile;
      const sowTemplate = (window as any).tempSOWTemplate;

      if (sowFile) {
        // Both files are ready, process them
        handleDualFileUpload(sowFile, addendumFile);
        (window as any).tempSOWFile = null;
        (window as any).tempAddendumFile = null;
      } else if (sowTemplate) {
        // SOW template from dashboard + addendum file
        handleSOWTemplateAndAddendumUpload(sowTemplate, addendumFile);
        (window as any).tempSOWTemplate = null;
        (window as any).tempAddendumFile = null;
      } else {
        showNotification('Addendum template ready. Please upload SOW file or select from dashboard.', 'success');
      }
    }
  };

  // Handle addendum generation
  const handleGenerateAddendum = async () => {
    if (!sowTemplate || !addendumTemplate || !addendumAnalysis) {
      setError('Missing required data for addendum generation');
      return;
    }

    setIsProcessing(true);
    setError(null);
    setStep('processing');

    try {
      console.log('🚀 ADDENDUM GENERATION: Starting processing...');

      // Collect form data
      const collectedFormData: Record<string, string> = {};

      // Get values from form inputs (this would be enhanced with proper form handling)
      addendumAnalysis.addendumRequiredFields.forEach(field => {
        const element = document.querySelector(`input[data-field="${field.field}"], textarea[data-field="${field.field}"]`) as HTMLInputElement;
        if (element) {
          collectedFormData[field.field] = element.value;
        }
      });

      // Process the addendum
      const processResponse = await fetch('/api/addendum/process', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sowMarkdown: sowTemplate.markdown,
          addendumMarkdown: addendumTemplate.markdown,
          sowTemplateId: sowTemplate.id,
          addendumTemplateId: addendumTemplate.id,
          userPrompt: userPrompt,
          sowContext: addendumAnalysis.sowContext,
          formData: collectedFormData
        }),
      });

      if (!processResponse.ok) {
        const errorData = await processResponse.json();
        throw new Error(errorData.error || 'Processing failed');
      }

      const processResult = await processResponse.json();
      setProcessedMarkdown(processResult.updatedMarkdown);

      console.log('✅ ADDENDUM GENERATION: Processing complete');
      setStep('preview');

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Generation failed');
      setStep('form');
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle addendum download
  const handleDownloadAddendum = async () => {
    if (!processedMarkdown || !addendumTemplate) {
      setError('No processed addendum available for download');
      return;
    }

    try {
      console.log('📥 ADDENDUM DOWNLOAD: Starting conversion...');

      // Save as template if requested
      if (saveAsTemplate && templateName.trim()) {
        try {
          const templateResponse = await fetch('/api/templates', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              name: templateName.trim(),
              content: addendumTemplate.markdown,
              type: 'addendum',
              userId: 'default-user' // This would come from auth context
            }),
          });

          if (templateResponse.ok) {
            showNotification('Template saved successfully!', 'success');
          }
        } catch (templateError) {
          console.error('Failed to save template:', templateError);
        }
      }

      const convertResponse = await fetch('/api/addendum/convert', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          addendumTemplateId: addendumTemplate.id,
          markdown: processedMarkdown,
          clientName: addendumAnalysis?.sowContext.find(c => c.field.toLowerCase().includes('client'))?.value || 'Client'
        }),
      });

      if (!convertResponse.ok) {
        const errorData = await convertResponse.json();
        throw new Error(errorData.error || 'Conversion failed');
      }

      // Download the file
      const blob = await convertResponse.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `addendum-${Date.now()}.docx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      console.log('✅ ADDENDUM DOWNLOAD: Download complete');
      showNotification('Addendum downloaded successfully!', 'success');

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Download failed');
    }
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Floating Elements */}
      <div className="absolute top-20 right-20 w-4 h-4 bg-blue-400/20 rounded-full animate-bounce delay-300"></div>
      <div className="absolute top-40 left-20 w-3 h-3 bg-purple-400/20 rounded-full animate-bounce delay-700"></div>
      <div className="absolute bottom-40 right-40 w-5 h-5 bg-pink-400/20 rounded-full animate-bounce delay-1000"></div>

      <div className="relative z-10 container mx-auto px-6 pt-32 pb-12">
        <div className="text-center mb-12">
          <h1 className="text-5xl font-bold text-white mb-6">
            Addendum Generator
          </h1>
          <p className="text-xl text-slate-300 max-w-3xl mx-auto">
            Generate professional contract addendums by analyzing your completed SOW and addendum template with AI-powered intelligence.
          </p>
        </div>

        {/* Progress Steps */}
        <div className="flex justify-center mb-12">
          <div className="flex items-center space-x-4">
            {['Upload', 'Analysis', 'Form', 'Processing', 'Preview'].map((stepName, index) => {
              const stepKey = stepName.toLowerCase() as typeof step;
              const isActive = step === stepKey;
              const isCompleted = ['upload', 'analysis', 'form', 'processing', 'preview'].indexOf(step) > index;
              
              return (
                <div key={stepName} className="flex items-center">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold transition-all duration-300 ${
                    isActive 
                      ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg' 
                      : isCompleted 
                        ? 'bg-green-500 text-white' 
                        : 'bg-white/10 text-slate-400'
                  }`}>
                    {isCompleted ? '✓' : index + 1}
                  </div>
                  {index < 4 && (
                    <div className={`w-12 h-1 mx-2 transition-all duration-300 ${
                      isCompleted ? 'bg-green-500' : 'bg-white/10'
                    }`} />
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="max-w-4xl mx-auto mb-8">
            <div className="bg-red-500/10 border border-red-500/20 rounded-xl p-4">
              <div className="flex items-center">
                <svg className="w-5 h-5 text-red-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-red-300">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Step Content */}
        {step === 'upload' && (
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* SOW File Upload */}
              <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8">
                <h3 className="text-xl font-bold text-white mb-4 text-center">Original SOW Document</h3>
                <div
                  className={`border-2 border-dashed rounded-2xl p-8 text-center transition-all duration-300 ${
                    uploadSuccess.sow
                      ? 'border-green-500 bg-green-500/10'
                      : dragActive === 'sow'
                        ? 'border-blue-500 bg-blue-500/10'
                        : 'border-white/30 hover:border-white/50'
                  }`}
                  onDrop={handleSOWDrop}
                  onDragOver={(e) => {
                    e.preventDefault();
                    setDragActive('sow');
                  }}
                  onDragLeave={() => setDragActive(null)}
                >
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4 ${
                    uploadSuccess.sow
                      ? 'bg-gradient-to-r from-green-500 to-emerald-600'
                      : 'bg-gradient-to-r from-blue-500 to-purple-600'
                  }`}>
                    {uploadSuccess.sow ? (
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    ) : (
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    )}
                  </div>
                  {uploadSuccess.sow ? (
                    <div>
                      <h4 className="text-lg font-bold text-green-300 mb-2">✓ SOW Uploaded Successfully</h4>
                      <p className="text-green-400 mb-4 text-sm">{uploadedFiles.sow}</p>
                    </div>
                  ) : (
                    <div>
                      <h4 className="text-lg font-bold text-white mb-2">Drop completed SOW here</h4>
                      <p className="text-slate-400 mb-4 text-sm">or click to browse</p>
                    </div>
                  )}
                  <input
                    type="file"
                    accept=".docx"
                    onChange={handleSOWFileInput}
                    className="hidden"
                    id="sow-upload"
                  />
                  <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    <label
                      htmlFor="sow-upload"
                      className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 font-bold transition-all duration-300 cursor-pointer inline-block text-center min-w-[180px]"
                    >
                      {uploadSuccess.sow ? 'Change SOW File' : 'Select SOW File'}
                    </label>
                    <button
                      onClick={() => {
                        setShowSOWSelector(true);
                        fetchAvailableSOWs();
                      }}
                      className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl hover:from-purple-700 hover:to-pink-700 font-bold transition-all duration-300 text-center min-w-[180px]"
                    >
                      Choose from Dashboard
                    </button>
                  </div>
                </div>
              </div>

              {/* Addendum Template Upload */}
              <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8">
                <h3 className="text-xl font-bold text-white mb-4 text-center">Addendum Template</h3>
                <div
                  className={`border-2 border-dashed rounded-2xl p-8 text-center transition-all duration-300 ${
                    uploadSuccess.addendum
                      ? 'border-green-500 bg-green-500/10'
                      : dragActive === 'addendum'
                        ? 'border-green-500 bg-green-500/10'
                        : 'border-white/30 hover:border-white/50'
                  }`}
                  onDrop={handleAddendumDrop}
                  onDragOver={(e) => {
                    e.preventDefault();
                    setDragActive('addendum');
                  }}
                  onDragLeave={() => setDragActive(null)}
                >
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4 ${
                    uploadSuccess.addendum
                      ? 'bg-gradient-to-r from-green-500 to-emerald-600'
                      : 'bg-gradient-to-r from-green-500 to-teal-600'
                  }`}>
                    {uploadSuccess.addendum ? (
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    ) : (
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    )}
                  </div>
                  {uploadSuccess.addendum ? (
                    <div>
                      <h4 className="text-lg font-bold text-green-300 mb-2">✓ Template Uploaded Successfully</h4>
                      <p className="text-green-400 mb-4 text-sm">{uploadedFiles.addendum}</p>
                    </div>
                  ) : (
                    <div>
                      <h4 className="text-lg font-bold text-white mb-2">Drop addendum template here</h4>
                      <p className="text-slate-400 mb-4 text-sm">or click to browse</p>
                    </div>
                  )}
                  <input
                    type="file"
                    accept=".docx"
                    onChange={handleAddendumFileInput}
                    className="hidden"
                    id="addendum-upload"
                  />
                  <label
                    htmlFor="addendum-upload"
                    className="px-6 py-3 bg-gradient-to-r from-green-600 to-teal-600 text-white rounded-xl hover:from-green-700 hover:to-teal-700 font-bold transition-all duration-300 cursor-pointer inline-block"
                  >
                    {uploadSuccess.addendum ? 'Change Template' : 'Select Template'}
                  </label>
                </div>
              </div>
            </div>

            {/* Processing Indicator */}
            {isProcessing && (
              <div className="text-center mt-8">
                <div className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl">
                  <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent mr-3"></div>
                  <span className="text-white font-medium">Processing files...</span>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Analysis Step */}
        {step === 'analysis' && addendumAnalysis && (
          <div className="max-w-4xl mx-auto">
            <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8">
              <h3 className="text-2xl font-bold text-white mb-6">Document Analysis Complete</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div className="bg-white/5 rounded-xl p-6">
                  <h4 className="text-lg font-semibold text-white mb-3">SOW Context Extracted</h4>
                  <p className="text-slate-300 text-sm mb-2">{addendumAnalysis.sowContext.length} fields identified</p>
                  <div className="space-y-2">
                    {addendumAnalysis.sowContext.slice(0, 3).map((context, index) => (
                      <div key={index} className="text-xs text-slate-400">
                        <span className="font-medium">{context.field}:</span> {context.value}
                      </div>
                    ))}
                  </div>
                </div>

                <div className="bg-white/5 rounded-xl p-6">
                  <h4 className="text-lg font-semibold text-white mb-3">Addendum Requirements</h4>
                  <p className="text-slate-300 text-sm mb-2">{addendumAnalysis.addendumRequiredFields.length} fields required</p>
                  <p className="text-slate-300 text-sm mb-2">Type: {addendumAnalysis.addendumType.replace('_', ' ')}</p>
                  <p className="text-slate-300 text-sm">Complexity: {addendumAnalysis.complexity}</p>
                </div>
              </div>

              <div className="text-center">
                <button
                  onClick={() => setStep('form')}
                  className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 font-bold transition-all duration-300"
                >
                  Continue to Form
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Form Step */}
        {step === 'form' && addendumAnalysis && (
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Main Form */}
              <div className="lg:col-span-2">
                <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8">
                  <h3 className="text-2xl font-bold text-white mb-6">Complete Addendum Information</h3>

                  {/* Pre-filled SOW Context */}
                  <div className="mb-8">
                    <h4 className="text-lg font-semibold text-white mb-4">Information from Original SOW</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {addendumAnalysis.sowContext.map((context, index) => (
                        <div key={index} className="bg-green-500/10 border border-green-500/20 rounded-xl p-4">
                          <label className="block text-sm font-medium text-green-300 mb-1">
                            {context.field.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                          </label>
                          <div className="text-white font-medium">{context.value}</div>
                          <div className="text-xs text-green-400 mt-1">
                            Confidence: {Math.round(context.confidence * 100)}%
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Required Addendum Fields */}
                  <div className="mb-8">
                    <h4 className="text-lg font-semibold text-white mb-4">Required Addendum Information</h4>
                    <div className="space-y-4">
                      {addendumAnalysis.addendumRequiredFields.map((field, index) => (
                        <div key={index} className="bg-white/5 border border-white/10 rounded-xl p-4">
                          <label className="block text-sm font-medium text-white mb-2">
                            {field.label}
                            {field.required && <span className="text-red-400 ml-1">*</span>}
                          </label>
                          {field.type === 'textarea' ? (
                            <textarea
                              data-field={field.field}
                              className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              rows={3}
                              placeholder={field.description || `Enter ${field.label.toLowerCase()}`}
                              onChange={(e) => setFormData(prev => ({ ...prev, [field.field]: e.target.value }))}
                              value={formData[field.field] || ''}
                            />
                          ) : (
                            <input
                              data-field={field.field}
                              type={field.type}
                              className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder={field.description || `Enter ${field.label.toLowerCase()}`}
                              onChange={(e) => setFormData(prev => ({ ...prev, [field.field]: e.target.value }))}
                              value={formData[field.field] || ''}
                            />
                          )}
                          {field.description && (
                            <p className="text-xs text-slate-400 mt-1">{field.description}</p>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Optional Fields */}
                  {addendumAnalysis.addendumOptionalFields.length > 0 && (
                    <div className="mb-8">
                      <h4 className="text-lg font-semibold text-white mb-4">Optional Information</h4>
                      <div className="space-y-4">
                        {addendumAnalysis.addendumOptionalFields.map((field, index) => (
                          <div key={index} className="bg-white/5 border border-white/10 rounded-xl p-4">
                            <label className="block text-sm font-medium text-slate-300 mb-2">
                              {field.label}
                            </label>
                            {field.type === 'textarea' ? (
                              <textarea
                                data-field={field.field}
                                className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                rows={3}
                                placeholder={`Enter ${field.label.toLowerCase()} (optional)`}
                                onChange={(e) => setFormData(prev => ({ ...prev, [field.field]: e.target.value }))}
                                value={formData[field.field] || ''}
                              />
                            ) : (
                              <input
                                data-field={field.field}
                                type={field.type}
                                className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder={`Enter ${field.label.toLowerCase()} (optional)`}
                                onChange={(e) => setFormData(prev => ({ ...prev, [field.field]: e.target.value }))}
                                value={formData[field.field] || ''}
                              />
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* User Prompt */}
                  <div className="mb-8">
                    <h4 className="text-lg font-semibold text-white mb-4">Additional Instructions (Optional)</h4>
                    <textarea
                      value={userPrompt}
                      onChange={(e) => setUserPrompt(e.target.value)}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      rows={4}
                      placeholder="Provide any specific instructions or requirements for the addendum generation..."
                    />
                  </div>

                  <div className="text-center">
                    <button
                      onClick={handleGenerateAddendum}
                      disabled={isProcessing}
                      className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 font-bold transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isProcessing ? 'Generating...' : 'Generate Addendum'}
                    </button>
                  </div>
                </div>
              </div>

              {/* Smart Analysis Sidebar */}
              <div className="lg:col-span-1">
                <div className="bg-gradient-to-b from-purple-500/10 to-blue-500/10 backdrop-blur-xl border border-purple-400/30 rounded-3xl p-6 sticky top-8">
                  <h3 className="text-xl font-bold text-white mb-4 flex items-center">
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                    Smart Analysis
                  </h3>

                  {/* Addendum Type */}
                  <div className="mb-6">
                    <h4 className="text-sm font-semibold text-purple-300 mb-2">Addendum Type</h4>
                    <div className="bg-white/5 rounded-lg p-3">
                      <span className="text-white capitalize">{addendumAnalysis.addendumType.replace('_', ' ')}</span>
                    </div>
                  </div>

                  {/* Complexity */}
                  <div className="mb-6">
                    <h4 className="text-sm font-semibold text-purple-300 mb-2">Complexity Level</h4>
                    <div className="bg-white/5 rounded-lg p-3">
                      <span className={`capitalize ${
                        addendumAnalysis.complexity === 'low' ? 'text-green-400' :
                        addendumAnalysis.complexity === 'medium' ? 'text-yellow-400' :
                        'text-red-400'
                      }`}>
                        {addendumAnalysis.complexity}
                      </span>
                    </div>
                  </div>

                  {/* Recommendations */}
                  {addendumAnalysis.recommendations.length > 0 && (
                    <div className="mb-6">
                      <h4 className="text-sm font-semibold text-purple-300 mb-2">Recommendations</h4>
                      <div className="space-y-2">
                        {addendumAnalysis.recommendations.map((rec, index) => (
                          <div key={index} className="bg-white/5 rounded-lg p-3 text-sm text-slate-300">
                            {rec}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Cross References */}
                  {addendumAnalysis.crossReferences.length > 0 && (
                    <div className="mb-6">
                      <h4 className="text-sm font-semibold text-purple-300 mb-2">SOW References</h4>
                      <div className="space-y-2">
                        {addendumAnalysis.crossReferences.map((ref, index) => (
                          <div key={index} className="bg-white/5 rounded-lg p-3 text-sm text-slate-300">
                            <div className="font-medium">{ref.addendumField}</div>
                            <div className="text-xs text-slate-400">{ref.sowReference}</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Processing Step */}
        {step === 'processing' && (
          <div className="max-w-4xl mx-auto text-center">
            <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-12">
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-white border-t-transparent mx-auto mb-6"></div>
              <h3 className="text-2xl font-bold text-white mb-4">Generating Your Addendum</h3>
              <p className="text-slate-300 mb-6">
                Our AI is processing your SOW context and addendum requirements to create a professional contract addendum...
              </p>
              <div className="space-y-2 text-sm text-slate-400">
                <div>✓ Analyzing SOW context</div>
                <div>✓ Processing addendum template</div>
                <div>⏳ Generating cross-references</div>
                <div>⏳ Finalizing document</div>
              </div>
            </div>
          </div>
        )}

        {/* Preview Step */}
        {step === 'preview' && processedMarkdown && (
          <div className="max-w-6xl mx-auto">
            <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8">
              <h3 className="text-2xl font-bold text-white mb-6">Addendum Preview</h3>

              {/* Document Preview */}
              <div className="bg-white/5 rounded-xl p-6 mb-8 max-h-96 overflow-y-auto">
                <div className="prose prose-invert max-w-none">
                  <pre className="whitespace-pre-wrap text-sm text-slate-300 font-mono">
                    {processedMarkdown.substring(0, 2000)}
                    {processedMarkdown.length > 2000 && '...\n\n[Content truncated for preview]'}
                  </pre>
                </div>
              </div>

              {/* Document Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
                <div className="bg-white/5 rounded-xl p-4 text-center">
                  <div className="text-2xl font-bold text-white mb-1">
                    {Math.round(processedMarkdown.length / 5)}
                  </div>
                  <div className="text-sm text-slate-400">Estimated Words</div>
                </div>
                <div className="bg-white/5 rounded-xl p-4 text-center">
                  <div className="text-2xl font-bold text-white mb-1">
                    {processedMarkdown.split('\n').length}
                  </div>
                  <div className="text-sm text-slate-400">Lines</div>
                </div>
                <div className="bg-white/5 rounded-xl p-4 text-center">
                  <div className="text-2xl font-bold text-green-400 mb-1">
                    ✓
                  </div>
                  <div className="text-sm text-slate-400">Ready for Download</div>
                </div>
              </div>

              {/* Template Saving Options */}
              <div className="mb-6 p-4 bg-white/5 rounded-xl border border-white/10">
                <div className="flex items-center mb-3">
                  <input
                    type="checkbox"
                    id="save-template"
                    checked={saveAsTemplate}
                    onChange={(e) => setSaveAsTemplate(e.target.checked)}
                    className="mr-3 w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <label htmlFor="save-template" className="text-white font-medium">
                    Save addendum template for future use
                  </label>
                </div>
                {saveAsTemplate && (
                  <input
                    type="text"
                    placeholder="Enter template name..."
                    value={templateName}
                    onChange={(e) => setTemplateName(e.target.value)}
                    className="w-full px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                )}
              </div>

              <div className="flex justify-center space-x-4">
                <button
                  onClick={() => setStep('form')}
                  className="px-6 py-3 bg-white/10 border border-white/20 text-white rounded-xl hover:bg-white/20 transition-all duration-300"
                >
                  Back to Edit
                </button>
                <button
                  onClick={handleDownloadAddendum}
                  className="px-8 py-3 bg-gradient-to-r from-green-600 to-teal-600 text-white rounded-xl hover:from-green-700 hover:to-teal-700 font-bold transition-all duration-300"
                >
                  Download Addendum
                </button>
              </div>
            </div>
          </div>
        )}

        {/* SOW Selector Modal */}
        {showSOWSelector && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8 max-w-4xl w-full max-h-[80vh] overflow-y-auto">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-2xl font-bold text-white">Select SOW from Dashboard</h3>
                <button
                  onClick={() => setShowSOWSelector(false)}
                  className="text-white/60 hover:text-white transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {availableSOWs.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-slate-400">No completed SOWs found in your dashboard.</p>
                </div>
              ) : (
                <div className="grid gap-3">
                  {availableSOWs.map((sow) => (
                    <div
                      key={sow.id}
                      onClick={() => handleSOWSelection(sow)}
                      className="group relative overflow-hidden bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl hover:bg-white/15 hover:border-white/30 cursor-pointer transition-all duration-300 hover:scale-[1.01] hover:shadow-lg"
                    >
                      {/* Gradient overlay on hover */}
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                      <div className="relative p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center flex-1">
                            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mr-3 group-hover:scale-105 transition-transform duration-300">
                              <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                              </svg>
                            </div>
                            <div className="flex-1 min-w-0">
                              <h4 className="text-white font-bold text-sm mb-1 group-hover:text-blue-200 transition-colors duration-300 truncate">
                                {sow.name || `SOW-${sow.id}`}
                              </h4>
                              <div className="flex items-center space-x-2 mb-2">
                                <span className="px-2 py-0.5 bg-blue-500/20 text-blue-300 text-xs font-medium rounded">
                                  Document
                                </span>
                                {sow.status && (
                                  <span className="px-2 py-0.5 bg-green-500/20 text-green-300 text-xs font-medium rounded">
                                    {sow.status}
                                  </span>
                                )}
                              </div>
                              <div className="space-y-1">
                                <div className="flex items-center text-xs text-slate-400">
                                  <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4h3a1 1 0 011 1v9a1 1 0 01-1 1H5a1 1 0 01-1-1V8a1 1 0 011-1h3z" />
                                  </svg>
                                  <span>Created: {new Date(sow.createdAt).toLocaleDateString()}</span>
                                </div>
                                {sow.clientName && (
                                  <div className="flex items-center text-xs text-slate-400">
                                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                    <span>Client: {sow.clientName}</span>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center justify-center w-6 h-6 rounded-full bg-white/10 group-hover:bg-blue-500/20 transition-all duration-300 group-hover:scale-105 ml-3">
                            <svg className="w-3 h-3 text-blue-400 group-hover:text-blue-300 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                            </svg>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AddendumGeneratorPage;
